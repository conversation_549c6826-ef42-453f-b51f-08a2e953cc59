import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { signUpSchema } from '$lib/schemas/auth';
import { PUBLIC_VERCEL_URL } from '$env/static/public';
import { dev } from '$app/environment';

export const load: PageServerLoad = async () => {
	const form = await superValidate(zod(signUpSchema));
	return { form };
};

export const actions: Actions = {
	default: async ({ request, locals }) => {
		const form = await superValidate(request, zod(signUpSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const emailRedirectTo = `${dev ? 'http' : 'https'}://${PUBLIC_VERCEL_URL}/auth/signin`;

		const { error } = await locals.supabase.auth.signUp({
			email: form.data.email,
			password: form.data.password,
			options: {
				emailRedirectTo,
			},
		});

		if (error) {
			console.error({ error, email: form.data.email });
			// return fail(400, { form, message: error.message });
			return message(form, { type: 'error', text: error.message });
		}

		return message(form, {
			type: 'success',
			text: 'Account created successfully. Please check your email to verify.',
		});
	},
};
